package main

import (
	"bufio"
	"fmt"
	"log"
	"os"
	"regexp"
	"strings"
)

type Converter struct {
	inputFile    string
	outputFile   string
	currentTable string
	indexes      []string // 存储需要单独创建的索引
}

func NewConverter(inputFile, outputFile string) *Converter {
	return &Converter{
		inputFile:    inputFile,
		outputFile:   outputFile,
		currentTable: "",
		indexes:      make([]string, 0),
	}
}

func (c *Converter) Convert() error {
	// 打开输入文件
	input, err := os.Open(c.inputFile)
	if err != nil {
		return fmt.Errorf("无法打开输入文件: %v", err)
	}
	defer input.Close()

	// 创建输出文件
	output, err := os.Create(c.outputFile)
	if err != nil {
		return fmt.Errorf("无法创建输出文件: %v", err)
	}
	defer output.Close()

	scanner := bufio.NewScanner(input)
	// 增加缓冲区大小以处理长行
	buf := make([]byte, 0, 64*1024)
	scanner.Buffer(buf, 1024*1024) // 1MB 缓冲区

	writer := bufio.NewWriter(output)
	defer writer.Flush()

	// 写入 PostgreSQL 头部注释
	writer.WriteString("-- Converted from MySQL to PostgreSQL\n")
	writer.WriteString("-- Generated by mysql_to_pgsql_converter.go\n\n")

	lineNumber := 0
	for scanner.Scan() {
		lineNumber++
		line := scanner.Text()

		// 添加调试信息，帮助定位问题行
		if lineNumber%1000 == 0 {
			fmt.Printf("处理第 %d 行...\n", lineNumber)
		}

		convertedLine := c.convertLine(line)

		// 跳过空的转换结果
		if convertedLine != "" {
			writer.WriteString(convertedLine + "\n")
		}
	}

	if err := scanner.Err(); err != nil {
		return fmt.Errorf("读取文件时出错: %v", err)
	}

	// 在文件末尾添加收集到的索引
	if len(c.indexes) > 0 {
		writer.WriteString("\n-- 索引创建语句\n")
		for _, index := range c.indexes {
			writer.WriteString(index + "\n")
		}
	}

	return nil
}

func (c *Converter) convertLine(line string) string {
	// 去除首尾空格
	line = strings.TrimSpace(line)

	// 跳过 MySQL 特有的注释和设置
	if c.shouldSkipLine(line) {
		return ""
	}

	// 检测当前表名
	c.detectCurrentTable(line)

	// 转换各种 MySQL 语法到 PostgreSQL
	line = c.convertDataTypes(line)
	line = c.convertCharacterSet(line)
	line = c.convertEngine(line)
	line = c.convertIndexes(line)
	line = c.convertQuotes(line)
	line = c.convertAutoIncrement(line)
	line = c.convertComments(line)
	line = c.convertLockTables(line)
	line = c.convertInsertStatements(line)

	return line
}

func (c *Converter) detectCurrentTable(line string) {
	// 检测CREATE TABLE语句来获取当前表名
	if strings.HasPrefix(strings.ToUpper(line), "CREATE TABLE") {
		// 提取表名
		parts := strings.Fields(line)
		if len(parts) >= 3 {
			tableName := strings.Trim(parts[2], "`\"")
			c.currentTable = tableName
		}
	}
}

func (c *Converter) shouldSkipLine(line string) bool {
	skipPatterns := []string{
		"/*!",
		"SET @",
		"SET NAMES",
		"SET TIME_ZONE",
		"SET UNIQUE_CHECKS",
		"SET FOREIGN_KEY_CHECKS",
		"SET SQL_MODE",
		"SET SQL_NOTES",
		"-- MySQL dump",
		"-- Host:",
		"-- Server version",
		"-- Dump completed",
	}

	for _, pattern := range skipPatterns {
		if strings.Contains(line, pattern) {
			return true
		}
	}

	return false
}

func (c *Converter) convertDataTypes(line string) string {
	// 数据类型转换映射
	typeMap := map[string]string{
		"int NOT NULL AUTO_INCREMENT": "SERIAL",
		"int AUTO_INCREMENT":          "SERIAL",
		"int NOT NULL":                "INTEGER NOT NULL",
		"int":                         "INTEGER",
		"datetime NOT NULL":           "TIMESTAMP NOT NULL",
		"datetime":                    "TIMESTAMP",
		"text":                        "TEXT",
	}

	for mysqlType, pgType := range typeMap {
		line = strings.ReplaceAll(line, mysqlType, pgType)
	}

	return line
}

func (c *Converter) convertCharacterSet(line string) string {
	// 移除字符集和排序规则
	charsetRegex := regexp.MustCompile(`CHARACTER SET \w+`)
	line = charsetRegex.ReplaceAllString(line, "")

	collateRegex := regexp.MustCompile(`COLLATE \w+`)
	line = collateRegex.ReplaceAllString(line, "")

	return strings.TrimSpace(line)
}

func (c *Converter) convertEngine(line string) string {
	// 移除 ENGINE 和 DEFAULT CHARSET
	engineRegex := regexp.MustCompile(`ENGINE=\w+`)
	line = engineRegex.ReplaceAllString(line, "")

	charsetRegex := regexp.MustCompile(`DEFAULT CHARSET=\w+`)
	line = charsetRegex.ReplaceAllString(line, "")

	return strings.TrimSpace(line)
}

func (c *Converter) convertIndexes(line string) string {
	// 转换索引语法
	if strings.Contains(line, "UNIQUE KEY") {
		// 处理唯一索引，移除长度限制
		uniqueRegex := regexp.MustCompile("UNIQUE KEY `([^`]+)` \\(`([^`]+)`\\((\\d+)\\)\\)")
		line = uniqueRegex.ReplaceAllString(line, "CONSTRAINT $1 UNIQUE ($2)")

		// 处理没有长度限制的唯一索引
		uniqueRegex2 := regexp.MustCompile("UNIQUE KEY `([^`]+)` \\(`([^`]+)`\\)")
		line = uniqueRegex2.ReplaceAllString(line, "CONSTRAINT $1 UNIQUE ($2)")
	}

	if strings.Contains(line, "KEY ") && !strings.Contains(line, "PRIMARY KEY") && !strings.Contains(line, "UNIQUE KEY") {
		// 普通索引转换为注释，因为需要在表创建后单独创建
		keyRegex := regexp.MustCompile("KEY `([^`]+)` \\(`([^`]+)`\\)")
		line = keyRegex.ReplaceAllString(line, "-- INDEX $1 ON table_name ($2)")
	}

	// 移除 USING BTREE
	line = strings.ReplaceAll(line, " USING BTREE", "")

	return line
}

func (c *Converter) convertQuotes(line string) string {
	// 将反引号转换为双引号
	line = strings.ReplaceAll(line, "`", "\"")
	return line
}

func (c *Converter) convertAutoIncrement(line string) string {
	// 移除 AUTO_INCREMENT 值设置
	autoIncRegex := regexp.MustCompile(`AUTO_INCREMENT=\d+`)
	line = autoIncRegex.ReplaceAllString(line, "")

	return strings.TrimSpace(line)
}

func (c *Converter) convertComments(line string) string {
	// 保留表注释，但调整语法
	if strings.Contains(line, "COMMENT=") {
		commentRegex := regexp.MustCompile(`COMMENT='([^']*)'`)
		line = commentRegex.ReplaceAllString(line, "-- $1")
	}

	// 处理列注释
	if strings.Contains(line, "COMMENT '") {
		commentRegex := regexp.MustCompile(`COMMENT '([^']*)'`)
		line = commentRegex.ReplaceAllString(line, "-- $1")
	}

	return line
}

func (c *Converter) convertLockTables(line string) string {
	// 移除 LOCK TABLES 和相关语句
	if strings.Contains(line, "LOCK TABLES") ||
		strings.Contains(line, "UNLOCK TABLES") ||
		strings.Contains(line, "ALTER TABLE") && strings.Contains(line, "DISABLE KEYS") ||
		strings.Contains(line, "ALTER TABLE") && strings.Contains(line, "ENABLE KEYS") {
		return ""
	}

	return line
}

func (c *Converter) escapeSingleQuotes(line string) string {
	// 简化的单引号转义方法
	// 将MySQL的\'转义转换为PostgreSQL的''转义

	// 先处理已经转义的单引号 \'
	line = strings.ReplaceAll(line, "\\'", "''")

	// 然后处理可能存在的其他单引号转义情况
	// 使用正则表达式更精确地处理VALUES子句中的内容
	if strings.Contains(line, "VALUES") {
		line = c.fixQuotesInValues(line)
	}

	return line
}

func (c *Converter) fixQuotesInValues(line string) string {
	// 找到VALUES部分并修复其中的引号问题
	valuesIndex := strings.Index(line, "VALUES")
	if valuesIndex == -1 {
		return line
	}

	prefix := line[:valuesIndex+6] // "VALUES"
	suffix := line[valuesIndex+6:]

	// 在VALUES部分中，将所有的\'替换为''
	// 这是一个更安全的方法，避免复杂的解析
	suffix = strings.ReplaceAll(suffix, "\\'", "''")

	// 处理可能存在的其他转义字符
	suffix = strings.ReplaceAll(suffix, "\\\"", "\"")
	suffix = strings.ReplaceAll(suffix, "\\\\", "\\")

	return prefix + suffix
}

func (c *Converter) convertInsertStatements(line string) string {
	// PostgreSQL 的 INSERT 语句基本兼容，但需要处理一些细节
	if strings.HasPrefix(line, "INSERT INTO") {
		// 确保使用双引号而不是反引号
		line = strings.ReplaceAll(line, "`", "\"")

		// 修复单引号转义问题
		line = c.escapeSingleQuotes(line)
	}

	return line
}

func main() {
	if len(os.Args) != 3 {
		fmt.Println("使用方法: go run mysql_to_pgsql_converter.go <输入文件> <输出文件>")
		fmt.Println("示例: go run mysql_to_pgsql_converter.go mishi_20250824153234n0ouy.sql output.sql")
		os.Exit(1)
	}

	inputFile := os.Args[1]
	outputFile := os.Args[2]

	converter := NewConverter(inputFile, outputFile)

	fmt.Printf("开始转换 %s 到 %s...\n", inputFile, outputFile)

	if err := converter.Convert(); err != nil {
		log.Fatalf("转换失败: %v", err)
	}

	fmt.Println("转换完成！")
	fmt.Printf("PostgreSQL 文件已保存为: %s\n", outputFile)
	fmt.Println("\n注意事项:")
	fmt.Println("1. 请检查转换后的文件，确保数据类型正确")
	fmt.Println("2. 可能需要手动创建索引")
	fmt.Println("3. 建议在导入前先测试小部分数据")
}
