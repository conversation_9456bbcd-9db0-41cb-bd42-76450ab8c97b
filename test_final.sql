-- 测试修复后的SQL语法
DROP TABLE IF EXISTS "dog_words";
CREATE TABLE "dog_words" (
"id" SERIAL , -- 主键
"content" TEXT   NOT NULL , -- 内容
"create_time" TIMESTAMP NOT NULL , -- 创建时间
"update_time" TIMESTAMP NOT NULL , -- 更新时间
PRIMARY KEY ("id"),
CONSTRAINT unique_content UNIQUE (content)
);

DROP TABLE IF EXISTS "image_urls";
CREATE TABLE "image_urls" (
"id" SERIAL , -- 自增主键
"url" varchar(255) NOT NULL , -- 图片完整URL
"created_at" timestamp NULL DEFAULT CURRENT_TIMESTAMP , -- 记录创建时间
PRIMARY KEY ("id")
);

-- 测试插入数据
INSERT INTO "dog_words" ("content", "create_time", "update_time") 
VALUES ('测试内容', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

INSERT INTO "image_urls" ("url") 
VALUES ('https://example.com/test.jpg');

-- 查询测试
SELECT * FROM "dog_words";
SELECT * FROM "image_urls";
